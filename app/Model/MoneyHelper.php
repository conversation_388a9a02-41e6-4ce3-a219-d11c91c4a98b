<?php

namespace SuperKoderi;

use App\Model\ProductVariantPrice;

class MoneyHelper
{

	public static function getPriceWithoutVat(float $priceVat, float $dphRate): float
	{
//		$koef = round($dphRate / ($dphRate + 100), 4); // old DPH - 0.1736
		$koef = $dphRate / ($dphRate + 100); // new DPH from 1. 4. 2019
		$dph = round($priceVat * $koef, ProductVariantPrice::PRICE_ROUND_PRECISION);
		return $priceVat - $dph;
	}


	public static function getNormalizedFloat(mixed $value, int $roundPrecision = 0): float
	{
		return round(floatval(str_replace(',', '.', trim($value))), $roundPrecision);
	}


	public static function getPriceWithVat(float $price, float $vatRateValue): float
	{
		return floatval($price * (max(0, $vatRateValue) / 100 + 1));
	}

	/**
	 * Vrati zaokrouhlene cislo na pocet deset. mist
	 */
	public static function round(int|float $value, int $precision = 0, ?callable $function = null): float
	{
		$mult = 10 ** max(0, $precision);
		$function ??= 'round';
		return $function($value * $mult) / $mult;
	}

	/**
	 * Vrati zaokrouhlene cislo smerem nahoru na pocet deset. mist
	 */
	public static function roundUp(int|float $value, int $precision = 0): float
	{
		return self::round($value, $precision, 'ceil');
	}

	/**
	 * Vrati zaokrouhlene cislo smerem dolu na pocet deset. mist
	 */
	public static function roundDown(int|float $value, int $precision = 0): float
	{
		return self::round($value, $precision, 'floor');
	}
}
