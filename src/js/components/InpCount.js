import { Controller } from '@hotwired/stimulus';

export default class InpCount extends Controller {
	static targets = ['inp', 'toolMinus', 'toolPlus'];
	static values = {
		min: Number,
		max: Number,
	};

	changeValue(event) {
		const value = parseInt(this.inpTarget.value) + parseInt(event.target.closest('[data-action="InpCount#changeValue"]').dataset.step);

		// change value
		this.inpTarget.value = value;

		// min value disabled minus
		if (this.minValue && value == this.minValue) {
			this.toolMinusTarget.classList.add('is-disabled');
		} else {
			this.toolMinusTarget.classList.remove('is-disabled');
		}

		// max value disabled plus
		if (this.maxValue && value == this.maxValue) {
			this.toolPlusTarget.classList.add('is-disabled');
		} else {
			this.toolPlusTarget.classList.remove('is-disabled');
		}

		// trigger change
		const evt = document.createEvent('HTMLEvents');
		evt.initEvent('change', false, true);
		this.inpTarget.dispatchEvent(evt);
	}
}
