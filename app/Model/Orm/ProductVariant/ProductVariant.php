<?php declare(strict_types=1);

namespace App\Model;

use App\Utils\DateTime;
use ArrayIterator;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\IRelationshipCollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\DeliveryDate;
use SuperKoderi\hasCacheTrait;
use SuperKoderi\hasTranslatorTrait;
use SuperKoderi\MoneyHelper;

/**
 * @property int $id {primary}
 * @property string $ean {default 0}
 * @property string $code {default ''}
 * @property DateTimeImmutable|null $created {default 'now'}
 * @property int|null $createdBy
 * @property DateTimeImmutable|null $edited {default 'now'}
 * @property int|null $editedBy
 * @property int $sort {default 0}
 * @property int $soldCount {default 0}
 * @property int|null $isInDiscount
 * @property string|null $extId
 *
 *
 * RELATIONS
 * @property Product $product {m:1 Product::$variants}
 * @property ParameterValue|null $param1Value {m:1 ParameterValue::$variants1}
 * @property ParameterValue|null $param2Value {m:1 ParameterValue::$variants2}
 * @property OneHasMany|Supply[] $supplies {1:m Supply::$variant, cascade=[persist, remove]}
 * @property ProductVariantLocalization[]|OneHasMany $variantLocalizations {1:m ProductVariantLocalization::$variant, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property ProductVariantPrice[]|OneHasMany $prices {1:m ProductVariantPrice::$productVariant, cascade=[persist, remove]}
 * @property Discount[]|ManyHasMany $discounts {m:m Discount::$variants, cascade=[persist, remove]}
 *
 *
 * VIRTUAL
 * @property-read string $url {virtual}
 * @property-read string|null $cf {virtual}
 * @property-read array|null $path {virtual}
 * @property-read string $stringId {virtual}
 * @property-read string $name {virtual}
 * @property-read string $nameAnchor {virtual}
 * @property-read string $nameTitle {virtual}
 * @property-read string $nameVariant {virtual}
 * @property-read string $nameVariantFormat {virtual}
 * @property-read string|null $content {virtual}
 *
 * @property-read int|null $param1Id {virtual}
 * @property-read int|null $param2Id {virtual}
 * @property-read int|null $param1ValueId {virtual}
 * @property-read int|null $param2ValueId {virtual}
 * @property-read int|null $paramValueIds {virtual}
 * @property-read string|null $template {virtual}
 * @property-read string|null $keywords {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string|null $annotation {virtual}
 * @property-read ProductImage|null $firstImage {virtual}
 * @property-read string|null $uid {virtual}
 * @property-read bool $active {virtual}
 *
 * @property-read array $pricesByLevel {virtual}
 *
 * @property-read bool $isVariant {virtual}
 * @property-read bool $isShell {virtual}
 *
 * @property-read int $isNew {virtual}
 *
 * @property-read array $suppliesByStock {virtual} sklady strukturovane podle ID
 * @property-read array $suppliesByStockAlias {virtual} sklady strukturovane podle aliasu skladu
 * @property-read bool $isInStock {virtual} globalne skladem
 * @property-read bool $isInStockDefault {virtual} skladem na centralnim skladu
 * @property-read bool $isInStockSupplier {virtual} skladem u dodavatelu
 * @property-read int $totalSupplyCount {virtual} pocet skladem celkem
 * @property-read int $suplyCountStockDefault {virtual} pocet skladem na centralnim skladu
 * @property-read int $suplyCountStockSupplier {virtual} pocet skladem u dodavatelu
 *
 * @property-read ProductContent[]|OneHasMany $contents {virtual}
 * @property-read ProductComment[]|OneHasMany $comments {virtual}
 * @property-read ProductReview[]|OneHasMany $reviews {virtual}
 * @property-read ProductFile[]|OneHasMany $files {virtual}
 * @property-read ProductTree[]|OneHasMany $productTrees {virtual}
 * @property-read ParameterValue[]|ManyHasMany $parametersValues {virtual}
 * @property-read Voucher|null $voucher {virtual}
 * @property-read mixed $links {virtual}
 * @property-read Tree[]|null $pages {virtual}
 * @property-read mixed $videos {virtual}
 * @property-read Product[]|null $products {virtual}
 * @property-read ProductImage[]|ArrayIterator $images {virtual}
 *
 *
 * @property-read string $cacheId {virtual}
 * @property-read string|null $googleCategory {virtual}
 * @property-read string|null $heurekaCategory {virtual}
 * @property-read string|null $zboziCategory {virtual}
 * @property-read Product[]|ICollection $accessories {virtual}
 * @property-read CatalogTree|null $mainCategory {virtual}
 *
 * @property-read int|null $priceDiscountDPH {virtual} // @todo jk price akce
 * @property-read string|null $editedString {virtual}
 */
class ProductVariant extends BaseEntity
{

	use hasCacheTrait;
	use hasTranslatorTrait;

	public const SHELL_STRING_ID = '0-0';

	private TreeRepository $treeRepository;

	private HolidayModel $holidayModel;


	private PriceLevelRepository $priceLevelRepository;
	private ?PriceLevel $discountPriceLevel = null;

	public function injectService(
		PriceLevelRepository $priceLevelRepository,
		TreeRepository $treeRepository,
		HolidayModel $holidayModel
	): void
	{
		$this->priceLevelRepository = $priceLevelRepository;
		$this->treeRepository = $treeRepository;
		$this->holidayModel = $holidayModel;
	}


	protected function getterActive(): bool
	{
		return (bool) $this->getLocalization(
			$this->product->getMutation()
		)->active;
	}

	public function getterStringId(): string
	{
		if (!isset($this->cache['stringId'])) {
			$stringId = [];
//			bd($this->param1Value);
			if ($this->param1Value) {
				$stringId[] = $this->param1Value->id;
			} else {
				$stringId[] = 0;
			}

			if ($this->param2Value) {
				$stringId[] = $this->param2Value->id;
			} else {
				$stringId[] = 0;
			}

			$this->cache['stringId'] = implode('-', $stringId);
		}

		return $this->cache['stringId'];
	}


	protected function getterUid(): ?string
	{
		return $this->product->uid;
	}

	// ************************************ Name ************************************************

	public function getterNameVariant(): string
	{
		if (!isset($this->cache['nameVariant'])) {
			$this->cache['nameVariant'] = $this->param1ValueId ? Strings::firstLower($this->translator->translate('pvalue_' . $this->param1ValueId)) : '';
		}

		return $this->cache['nameVariant'];
	}


	public function getterNameVariantFormat(): string
	{
		return $this->nameVariant ? sprintf(' (%s)', $this->nameVariant) : '';
	}


	protected function getterName(): string
	{
		return $this->product->name . $this->nameVariantFormat;
	}


	protected function getterNameAnchor(): string
	{
		return $this->product->nameAnchor . $this->nameVariantFormat;
	}


	protected function getterNameTitle(): string
	{
		return $this->product->nameTitle . $this->nameVariantFormat;
	}

	// ************************************ Param ************************************************

	public function getterParam1Id(): ?int
	{
		return $this->param1ValueId;
	}


	public function getterParam1ValueId(): ?int
	{
		return $this->param1Value ? $this->param1Value->id : null;
	}


	public function getterParam2Id(): ?int
	{
		return $this->param2ValueId;
	}


	public function getterParam2ValueId(): ?int
	{
		return $this->param2Value ? $this->param2Value->id : null;
	}


	protected function getterParamValueIds(): array
	{
		$ret = [];
		if (isset($this->param1Value->id)) {
			$ret[] = $this->param1Value->id;
		}

		if (isset($this->param2Value->id)) {
			$ret[] = $this->param2Value->id;
		}

		return $ret;
	}

	// ************************************************************************************

	protected function getterEditedString(): string
	{
		return $this->edited ? $this->edited->format('Y-m-d H:i:s') : '';
	}


	protected function getterVoucher(): ?Voucher
	{
		$product = $this->product;
		return $product->getLocalization($product->getMutation())->voucher;
	}


	protected function getterIsShell(): bool
	{
		return $this->stringId === self::SHELL_STRING_ID;
	}


	protected function getterPath(): ?array
	{
		return $this->product->path;
	}


	protected function getterTemplate(): string
	{
		return $this->product->template;
	}


	protected function getterKeywords(): ?string
	{
		return $this->product->keywords;
	}


	protected function getterDescription(): ?string
	{
		return $this->product->description;
	}


	protected function getterAnnotation(): ?string
	{
		return $this->product->annotation;
	}


	protected function getterMainCategory(): ?CatalogTree
	{
		return $this->product->mainCategory;
	}

	//TODO REF VOJTA ??
	protected function getterFirstImage(): ?ProductImage
	{
		//return $this->product->firstImage;
		return $this->images->current();
	}


	/**
	 * nalezeni obrazku pro danou variantu
	 * @return ArrayIterator<int, ProductImage>
	 */
	protected function getterImages(): ArrayIterator
	{
		$images = $this->product->images;
		$finalImages = [];
		foreach ($images as $i) {
			$toSearch = empty($i->variants) ? [] : explode('|', $i->variants);
			if (in_array($this->id, $toSearch)) {
				$finalImages[] = $i;
			} elseif ($i->variants === null || !$i->variants) {
				$finalImages[] = $i;
			}
		}

		return new ArrayIterator($finalImages);
	}



	// ************************************ Stock ************************************************


	protected function getterSuppliesByStock(): array
	{
		if (!isset($this->cache['suppliesByStock'])) {
			$supplies = [];
			foreach ($this->supplies as $supply) {
				$supplies[$supply->stock->id] = $supply;
			}

			$this->cache['suppliesByStock'] = $supplies;
		}

		return $this->cache['suppliesByStock'];
	}

	protected function getterSuppliesByStockAlias(): array
	{
		if (!isset($this->cache['suppliesByStockAlias'])) {
			$supplies = [];
			foreach ($this->suppliesByStock as $supply) {
				$supplies[$supply->stock->alias] = $supply;
			}

			$this->cache['suppliesByStockAlias'] = $supplies;
		}

		return $this->cache['suppliesByStockAlias'];
	}


	protected function getterTotalSupplyCount(): int
	{
		if (!isset($this->cache['totalSupplyCount'])) {
			$totalSupplyCount = 0;

			if ($this->product->isVoucher) {
				$totalSupplyCount = 10000;
			} else {
				foreach ($this->suppliesByStock as $stockId => $supply) {
					$totalSupplyCount += $supply->amount;
				}
			}

			$this->cache['totalSupplyCount'] = $totalSupplyCount;
		}

		return $this->cache['totalSupplyCount'];
	}


	protected function getterIsInStock(): bool
	{
		return $this->totalSupplyCount > 0;
	}

	// **** specificke zkratky pro konkretni sklady *******

	protected function getterIsInStockDefault(): bool
	{
		return !empty($this->suppliesByStockAlias[Stock::ALIAS_SHOP]->amount);
	}

	protected function getterSuplyCountStockDefault(): int
	{
		return $this->isInStockDefault ? $this->suppliesByStockAlias[Stock::ALIAS_SHOP]->amount : 0;
	}

	protected function getterIsInStockSupplier(): bool
	{
		return !empty($this->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE]->amount);
	}

	protected function getterSuplyCountStockSupplier(): int
	{
		return $this->isInStockSupplier ? $this->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE]->amount : 0;
	}

	// ************************************ Delivery Date ************************************************

	/**
	 * Vrati datum doruceni (DD)
	 *
	 * @param Mutation $mutation
	 * @param State $state
	 * @param MutationTransports|null $transport
	 * @param int $stockAmount pozadovany pocet na sklade
	 * @return DeliveryDate|null
	 */
	public function getDeliveryDate(Mutation $mutation, State $state, ?MutationTransports $transport = null, int $stockAmount = 1): ?DeliveryDate
	{
		// *********** expedice - sklady *******************

		$stockAmount = abs($stockAmount);

		if ($this->totalSupplyCount < $stockAmount) { // pozadovane mnozstvi neni skladem = DD nezname
			return null;
		}

		try {
			if ($stockAmount <= $this->suplyCountStockDefault) { // je skladem na centralnim skladu
				// rozhodujici hodina se bere z dopravy, jinak defaultni z daneho skladu
				$alias = Stock::ALIAS_SHOP;
				$hour = $transport && isset($transport->deliveryHourStock->$alias) ? $transport->deliveryHourStock->$alias : $this->suppliesByStockAlias[$alias]->stock->deliveryHour;
				$dateFrom = new DateTime($hour); // stat expedice bereme defaultne CZ (v $state je predpokladany stat doruceni) - pokud by byl sklad jinde nez v CZ, zde je potreba urcit stat skladu

				if ($dateFrom->isPast()) { // je vice nez rozhodujici hodina = pridame dalsi prac. den navic
					$dateFrom->addWorkday();
				}
			} else { // je skladem u dodavatele
				// rozhodujici hodina se bere z dopravy, jinak defaultni z daneho skladu
				$alias = Stock::ALIAS_SUPPLIER_STORE;
				$hour = $transport && isset($transport->deliveryHourStock->$alias) ? $transport->deliveryHourStock->$alias : $this->suppliesByStockAlias[$alias]->stock->deliveryHour;
				$dateFrom = new DateTime($hour); // stat expedice bereme defaultne CZ (v $state je predpokladany stat doruceni) - pokud by byl sklad jinde nez v CZ, zde je potreba urcit stat skladu
//				if ($mutation->isDefault) {
//					$dateFrom->addWorkday(); // CZ = +1 prac. den
//				}

				$dateFrom->addWorkday();

				if ($dateFrom->isPast()) { // je vice nez rozhodujici hodina = pridame pocet prac. dnu podle ERP
					$stockDays = 1; // @todo jk stock toto je uz Balistas
					$dateFrom->addWorkday($stockDays);
				}
			}

			// modul holiday - dovolena, ktera ovlivnuje expedici ze skladu
			foreach ($this->holidayModel->getAll() as $holiday) { // iterujeme chronologicky vsechny platne dovolene
				if ($dateFrom <= $holiday->publicTo->setTime(23, 59, 59)) { // DD je v terminu dovolene
					$dateFrom = DateTime::from($holiday->publicTo)->setHolidays($state)->addWorkday()->getClosestWorkday(); // DD je nejblizsi prac. den po dovolene
				}
			}

			$dateExpedition = clone $dateFrom;

			// *********** zacina doba dodani - prepravci *******************
			$dateTo = null;

			// doprava
			if ($transport) {
				if ($transport->deliveryDayFrom > 0) {
					$dateFrom->setHolidays($state); // nastavim svatky pro cilovou zemi doruceni
					$dateFrom->addWorkday($transport->deliveryDayFrom);
				}

				if ($transport->deliveryDayTo > 0) { // ma byt rozsah od-do
					$dateTo = $dateFrom->modifyClone(sprintf('+ %d day', $transport->deliveryDayTo));
					$dateTo->setHolidays($state); // nastavim svatky pro cilovou zemi doruceni
					$dateTo->getClosestWorkday(); // datum do posuneme na nejblizsi pracovni den
				}
			}

			return new DeliveryDate($dateFrom, $dateTo, $dateExpedition);

		} catch (\Throwable $e) {
			return null;
		}
	}

	// ************************************ Price ************************************************


	protected function getterPricesByLevel(): array
	{
		if (!isset($this->cache['pricesByLevel'])) {
			$prices = [];
			foreach ($this->prices as $price) {
				$prices[$price->mutation->id][$price->priceLevel->id] = $price;
			}

			$this->cache['pricesByLevel'] = $prices;
		}

		return $this->cache['pricesByLevel'];
	}


	private function fillPriceCache(Mutation $mutation, PriceLevel $priceLevel, State $state): void
	{
		if (!isset($this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'])) {

			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'] = null;
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'] = 0.0;
			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'] = 0.0;

			if (isset($this->pricesByLevel[$mutation->id][$priceLevel->id])) {
				/** @var ProductVariantPrice $productVariantPrice */
				$productVariantPrice = $this->pricesByLevel[$mutation->id][$priceLevel->id];



				$bestActiveDiscountPrice = (isset($this->pricesByLevel[$mutation->id][PriceLevel::TYPE_PRICE_ACTION_ID]))
					? $this->pricesByLevel[$mutation->id][PriceLevel::TYPE_PRICE_ACTION_ID] : null;

				/** @var ProductVariantPrice $bestActiveDiscountPrice */
				if ($bestActiveDiscountPrice !== null && $bestActiveDiscountPrice->price > 0 && $this->product->isAction) {
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'] = $bestActiveDiscountPrice;
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'] = $bestActiveDiscountPrice->price;
				} else {
					$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'] = $productVariantPrice->price;
				}

				$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'] = $productVariantPrice->price;
			}
		}
	}


	public function priceWithVat(Mutation $mutation, PriceLevel $priceLevel, State $state, bool $skipPriceLevelDetection = false): float
	{
		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['priceWithVat'];
	}


	public function discountPriceWithVat(Mutation $mutation, State $state): ?ProductVariantPrice
	{
		$priceLevel = $this->getDiscountPriceLevel();

		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['discountPriceWithVat'];
	}


	public function originalPriceWithVat(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id]['originalPriceWithVat'];
	}


	public function priceVat(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$value = MoneyHelper::getPriceWithoutVat($this->priceWithVat($mutation, $priceLevel, $state), $this->product->vat($state));
		return MoneyHelper::round($value, $mutation->currency->decimals);
	}

	public function originalPriceVat(Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$value = MoneyHelper::getPriceWithoutVat($this->originalPriceWithVat($mutation, $priceLevel, $state), $this->product->vat($state));
		return MoneyHelper::round($value, $mutation->currency->decimals);
	}

//
//	protected function getterPriceDiscountDPH(): int
//	{
//		return 0;// fallback
//		//return $this->priceFinalDPH - $this->priceVat(); // @todo jk price akce vrati rozdil puvodni cena minus cena po sleve
//	}



	// ***********************************************************************************


	protected function getterContent(): ?string
	{
		return $this->product->content;
	}


	protected function getterContents(): IRelationshipCollection
	{
		return $this->product->contents;
	}


	protected function getterFiles(): IRelationshipCollection
	{
		return $this->product->files;
	}


	protected function getterLinks(): mixed
	{
		return $this->product->links;
	}


	protected function getterPages(): ICollection
	{
		return $this->product->pages;
	}


	protected function getterVideos(): mixed
	{
		return $this->product->videos;
	}


	protected function getterProducts(): ICollection
	{
		return $this->product->products;
	}


	protected function getterIsNew(): int
	{
		return $this->product->isNew;
	}

	public function getParameterValueByUid(string $parameterUid): mixed
	{
		return $this->product->getParameterValueByUid($parameterUid);
	}


	public function getParameters(): array
	{
		return $this->product->getParameters();
	}

	public function getterAccessories(): ICollection
	{
		return $this->product->accessories;
	}


	public function getterIsVariant(): bool
	{
		return true;
	}

	public function getParentCat(): ?Tree
	{
		$parentId = null;
		if ($this->path) {
			$parentId = end($this->path);
		}

		if (!$parentId) {
			return $this->treeRepository->getByUid('eshop');
		}

		return $this->treeRepository->getById($parentId);
	}

	protected function getterCacheId(): string
	{
		return 'var' . $this->id;
	}


	protected function getterHeurekaCategory(): mixed
	{
		if (is_array($this->path)) {
			foreach (array_reverse($this->path) as $parentId) {
				$page = $this->treeRepository->getById($parentId);
				if (isset($page->cf->feeds) && isset($page->cf->feeds->heureka) && $page->cf->feeds->heureka) {
					return $page->cf->feeds->heureka;
				}
			}
		}

		return null;
	}


	protected function getterZboziCategory(): mixed
	{
		if (is_array($this->path)) {
			foreach (array_reverse($this->path) as $parentId) {
				$page = $this->treeRepository->getById($parentId);
				if (isset($page->cf->feeds) && isset($page->cf->feeds->zbozi) && $page->cf->feeds->zbozi) {
					return $page->cf->feeds->zbozi;
				}
			}
		}

		return null;
	}


	protected function getterGoogleCategory(): mixed
	{
		if (is_array($this->path)) {
			foreach (array_reverse($this->path) as $parentId) {
				$page = $this->treeRepository->getById($parentId);
				if (isset($page->cf->feeds) && isset($page->cf->feeds->google) && $page->cf->feeds->google) {
					return $page->cf->feeds->google;
				}
			}
		}

		return null;
	}

	protected function getterCf(): mixed
	{
		return $this->product->cf;
	}


	public function getLocalization(Mutation $mutation): ?ProductVariantLocalization
	{
		return $this->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
	}


	public function getFormId(): string
	{
		return ($this->isPersisted()) ? (string) $this->id : 'newItem';
	}

	public function getId(): int
	{
		return $this->id;
	}

	protected function getMutation(): Mutation
	{
		// TODO
		throw new \LogicException('ProductVariant must not be Routable, ProductVariantLocalization should be Routable instead.');
	}

	private function getDiscountPriceLevel(): PriceLevel
	{
		if ($this->discountPriceLevel === null) {
			$this->discountPriceLevel = $this->priceLevelRepository->getByChecked(['type'=>PriceLevel::TYPE_PRICE_ACTION]);
		}

		return $this->discountPriceLevel;
	}

}
