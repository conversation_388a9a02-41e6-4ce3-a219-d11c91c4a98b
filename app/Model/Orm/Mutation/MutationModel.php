<?php


namespace App\Model;


use Nette\Utils\ArrayHash;
use SuperKoderi\CustomField\CustomFields;
use SuperKoderi\MoneyHelper;

class MutationModel
{
	public function __construct(
		private Orm $orm,
		private CustomFields $customFields,
	) {}

	public function findForRouter(string $domain, string $prefix = ''): ?Mutation
	{
		static $cache = [];
		if (!isset($cache['routerInfo'])) {
			$mutations = $this->orm->mutation->findBy([
				'public' => 1
			])->fetchAll();
			$tmp = [];
			foreach ($mutations as $mutation) {
				$tmp[strtolower($mutation->getRealDomainWithoutWWW()) . '--' . strtolower($mutation->getRealUrlPrefix())] = $mutation;
			}

			$cache['routerInfo'] = $tmp;
		}


		$key = strtolower($domain) . '--' . strtolower($prefix);
		if (isset($cache['routerInfo'][$key])) {
			return $cache['routerInfo'][$key];
		}

		return null;
	}

	public function save(Mutation $mutation, ArrayHash $data, array $httpData): Mutation
	{
		$noSave = ['id'];
		$intValues = [];
		$boolValues = [];

		$this->handleCurrency($mutation, $data);

		foreach ($mutation->getMetadata()->getProperties() as $i) {
			$col = (string)$i->name;
			if (in_array($col, $noSave)) {
				continue;
			}
			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = (int)$data[$col];
				}
				$mutation->$col = $data[$col];
			} else {
				if (in_array($col, $boolValues)) {
					$mutation->$col = 0;
				}
			}
		}

		if (isset($httpData['customFields'])) {
			$mutation->cf = $this->customFields->prepareDataToSave($httpData['customFields']);
		}

		$this->orm->persistAndFlush($mutation);

		return $mutation;
	}

	private function handleCurrency(Mutation $mutation, mixed $data): void
	{
		if (!$mutation->isDefault && isset($data['exchangeRate'], $mutation->currency->exchangeRate)) {
			$currency = $mutation->currency;
			$currency->exchangeRate = MoneyHelper::getNormalizedFloat($data['exchangeRate'], 2);
			$mutation->currency = $currency;
			unset($data['exchangeRate']);
		}
	}


	public function delete(Mutation $mutation): void
	{
		$this->orm->mutation->removeAndFlush($mutation);
	}

}
