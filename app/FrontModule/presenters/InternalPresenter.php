<?php

namespace FrontModule;


use SuperKoderi\Components\IBasketRemoveFactory;

class InternalPresenter extends BasePresenter
{

	public function actionDefault(): void
	{
		$idref = $this->params['idref'];
		$this->setObject($object = $this->orm->tree->getById($idref));


		if ($uid = $object->uid) {

			switch ($uid) {
				case 'basketRemove':
					break;

				case 'popupGallery':
					$activeTab = 'photo';
					if (isset($this->params['tab']) && in_array($this->params['tab'], ['photo', 'video'])) {
						$activeTab = $this->params['tab'];
					}

					$activeItem = 1;
					if (isset($this->params['item'])) {
						$activeItem = (int)$this->params['item'];
					}

					if (isset($this->params['pageId'])) {
						$this->template->localObject = $this->orm->tree->getById($this->params['pageId']);
					}
					if (isset($this->params['productId'])) {
						$this->template->localObject = $this->orm->productVariant->getById($this->params['productId']);
					}
					$this->template->activeItem = $activeItem;
					$this->template->activeTab = $activeTab;


					if ($this->isAjax()) {
						$this->redrawControl('gallery');
					}
					break;
			}

			$this->setView($uid);
		}


	}

}
