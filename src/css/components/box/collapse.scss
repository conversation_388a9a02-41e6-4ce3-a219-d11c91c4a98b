@use 'config';

[data-controller='HeaderMenu'] {
	position: relative;
}

.b-collapse {
	&__content {
		display: none;
	}
	&__toggle {
		display: block;

		.icon-svg {
			width: 20px;
		}

		.btn {
			min-width: 42px;
			padding: 14px 5px;
		}
	}

	// STATES
	.is-collapsed-menu &__content {
		display: block;
	}

	// MQs
	@media (config.$md-down) {
		&__content {
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			z-index: 1;
			padding: 15px;
			background-color: #ffffff;
		}
	}
	@media (config.$md-up) {
		&__content {
			display: block;
		}

		&__toggle {
			display: none;
		}
	}
}
