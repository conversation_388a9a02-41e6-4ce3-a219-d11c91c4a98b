{varType SuperKoderi\Basket $basket}

{snippet form}
	{form form class=>'f-basket u-mb-lg', novalidate=>'novalidate'}
		<div class="u-mb-lg">
			<div class="grid grid--lg">
				<div class="grid__cell size--7-12@md">
					<h2 class="h3">{_'billing_info_title'}</h2>

					{control messageForForm, $flashes, $form}

					{include '../inp.latte', form=>$form, name=>email}
					{include '../inp.latte', form=>$form, name=>firstname}
					{include '../inp.latte', form=>$form, name=>lastname}
					{include '../inp.latte', form=>$form, name=>street}
					{include '../inp.latte', form=>$form, name=>city}
					{include '../inp.latte', form=>$form, name=>zip}
					{include '../inp.latte', form=>$form, name=>state}
					{include '../inp.latte', form=>$form, name=>phone, type=>'tel'}
					{include '../inp.latte', form=>$form, name=>infotext}

					{include '../../templates/part/form/part/company.latte', form=>$form}
					{if $basket->useDeliveryAddressForm()}
						{include '../../templates/part/form/part/delivery.latte', form=>$form}
					{/if}

					{include '../../templates/part/form/part/register.latte', form=>$form}

					{* {include '../inp.latte', form=>$form, name=>heurekaDisable, if=>isset($form['heurekaDisable'])} *}
					{include '../inp.latte', form=>$form, name=>agree, agreeLabel=>true, labelReplace=>"", if=>isset($form['agree'])}
					{*include '../inp.latte', form=>$form, name=>newsletter, if=>isset($form['newsletter'])*}
				</div>

				<div class="grid__cell size--5-12@md">
					<div class="u-sticky-container">
						{include '../../templates/part/box/cart-summary.latte'}
					</div>
				</div>
			</div>
		</div>

		<div class="b-order-buttons">
			<p class="b-order-buttons__prev">
				<a href="{plink $pages->step1}">
					{_'btn_back_order_step1'}
				</a>
			</p>
			<p class="b-order-buttons__next">
				<button type="submit" n:name="next" class="btn btn--lg js-steps-submit">
					<span class="btn__text">
						{_'btn_send_order'}
					</span>
				</button>
			</p>
		</div>
	{/form}
{/snippet}
