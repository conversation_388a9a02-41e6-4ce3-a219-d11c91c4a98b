{embed $templates.'/part/box/std.latte', props=>[
	title=> 'Stav',
]}
	{block content}

		<div class="u-mb-sm">
			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				{include $templates.'/part/core/checkbox.latte',
					props: [
						input: $localizationContainer['public'],
						label: '<span class="grid-inline"><span class="tag">'.strtoupper($langCode).'</span> <strong>Publikováno</strong></span>',
						classes: ['u-mb-xs']
					]
				}
			{/foreach}
		</div>


		{if $product->isVoucher === 0}
			{include $templates.'/part/core/checkbox.latte',
				props: [
					input: $form['productCommon']['isSet'],
					label: $form['productCommon']['isSet']->caption,
					classes: ['u-mb-xs']
				]
			}

			{include $templates.'/part/core/checkbox.latte',
				props: [
					input: $form['productCommon']['isMikrosvin'],
					label: $form['productCommon']['isMikrosvin']->caption,
					classes: ['u-mb-xs']
				]
			}
			{include $templates.'/part/core/checkbox.latte',
				props: [
					input: $form['productCommon']['isNew'],
					label: $form['productCommon']['isNew']->caption,
					classes: ['u-mb-xs']
				]
			}
			{include $templates.'/part/core/checkbox.latte',
				props: [
					input: $form['productCommon']['isAction'],
					label: $form['productCommon']['isAction']->caption,
					classes: ['u-mb-xs']
				]
			}
			{include $templates.'/part/core/checkbox.latte',
				props: [
					input: $form['productCommon']['isIconic'],
					label: $form['productCommon']['isIconic']->caption,
					classes: ['u-mb-xs']
				]
			}
			{include $templates.'/part/core/checkbox.latte',
				props: [
					input: $form['productCommon']['isSale'],
					label: $form['productCommon']['isSale']->caption,
					classes: ['u-mb-xs']
				]
			}


			{include $templates.'/part/core/inp.latte' props: [
				input: $form['productCommon']['isNewExpirationTime'],
				classes: ['u-mb-xs'],
				type: 'datetime-local'
			]}

		{/if}
	{/block}
{/embed}
