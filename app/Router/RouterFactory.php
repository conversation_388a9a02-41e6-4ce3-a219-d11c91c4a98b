<?php declare(strict_types = 1);

namespace App\Router;

use App\Model\Router\Filter;
use Nette\Application\Routers\Route;
use Nette\Application\Routers\RouteList;
use SuperKoderi\ConfigService;

final class RouterFactory
{

	public function __construct(
		private readonly ConfigService $configService,
		private readonly Filter $filter,
	)
	{
	}

	public function createRouter(): RouteList
	{
		$router = new RouteList();
		$router->addRoute('index.php', 'Front:Homepage:default', Route::ONE_WAY);
		$router->addRoute('/data/images-<type>/<id>.<ext jpg|jpeg|png|webp>', 'Front:Image:resample', Route::ONE_WAY);

		$router->addRoute('/geo', 'Front:Page:geo', Route::ONE_WAY);
		$router->addRoute('/invoice/<action>', 'Front:Invoice:default');

		$router->add($adminRouter = new RouteList('Admin'));
		$adminRouter->addRoute($this->configService->get('adminAlias') . '/ztracene-heslo', 'Sign:lostPassword');
		$adminRouter->addRoute($this->configService->get('adminAlias') . '/reset-hesla', 'Sign:resetPassword');
		$adminRouter->addRoute($this->configService->get('adminAlias') . '/<presenter>/<action>', 'Homepage:default');

		$router->add($frontRouter = new RouteList('Front'));

		$frontFilters = [
			null => [
				Route::FilterIn => $this->filter->in(...),
				Route::FilterOut => $this->filter->out(...),
			],
		];

		$frontRouter->addRoute('[<urlPrefix cs>/]<alias .*>', $frontFilters);
		$frontRouter->addRoute('[<urlPrefix cs>]', $frontFilters);

		return $router;
	}

}
