<?php

namespace SuperKoderi\Components;

use App\Model\Redirect;
use App\Model\RedirectModel;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Controls\TextInput;
use Nette\Http\Url;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\Translator;

/**
 * @property-read DefaultTemplate $template
 */
class RedirectForm extends UI\Control
{
	use hasOrmTrait;
	use hasConfigServiceTrait;

	public function __construct(
		private Translator $translator,
		private RedirectModel $redirectModel,
		private ?Redirect $redirect = null,
	) {}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->redirect = $this->redirect;
		$this->template->config = $this->configService->getParams();

		$this->template->render(__DIR__ . '/redirecForm.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addText('oldUrl', 'oldUrl')
//			->addRule([$this, 'isUrl'], 'Neplatná url')
			->setRequired();
		$form->addText('newUrl', 'newUrl')
//			->addRule([$this, 'isUrl'], 'Neplatná url')
			->setRequired();
		$codes = [301 => 301,
			302 => 302,
			303 => 303,
			307 => 307,
			308 => 308,
		];
		$form->addSelect('code', 'code', $codes)->setRequired();

		if ($this->redirect) {
			$form->setDefaults($this->redirect->getFormData($form));
		} else {
			$newRedirect = new Redirect();
			$form->setDefaults($newRedirect->getFormData($form));
		}

		$form->addSubmit('send');
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'formValidate'];

		return $form;
	}

	public function formValidate(UI\Form $form, ArrayHash $values): void
	{
		foreach (['oldUrl', 'newUrl'] as $name) {
			if (Strings::contains($values->$name, '#')) {
				$form[$name]->addError('error');
			}
		}
		if ($form->hasErrors()) {
			$this->flashMessage($this->translator->translate('msg_redirect_invalid_char'), 'error');
		}
	}


	/**
	 * @param UI\Form $form
	 * @param ArrayHash $values
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$this->redirect = $this->redirectModel->save($values->oldUrl, $values->newUrl, $values->code, $this->redirect);
		} catch (\Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException $e) {
			$this->flashMessage('redirect_error_msg_old_url_already_exists', 'error');
		}

		if ($this->redirect) {
			$this->redirectModel->generateRules();
			$this->flashMessage('OK', 'ok');
			$this->presenter->redirect('edit', ['id' => $this->redirect->id]);
		}
	}

	/**
	 * @throws AbortException
	 */
	public function handleDelete(): void
	{
		$this->redirectModel->delete($this->redirect);
		$this->redirectModel->generateRules();
		$this->presenter->redirect('default');
	}
}


interface IRedirectFormFactory
{
	public function create(?Redirect $redirect = null): RedirectForm;
}
