@use 'base/variables';
@use 'base/extends';
@use 'config';

.f-filter {
	$s: &;

	&__group {
		margin-bottom: 10px;
	}
	&__list {
		@extend %reset-ul;
		max-height: 400px;
		margin-bottom: 5px;
		padding-top: 10px;
		overflow-y: auto;
		box-shadow: 0 1px 0 0 #f3ebdf;

		&::-webkit-scrollbar {
			width: 6px;
		}
		&::-webkit-scrollbar-thumb {
			background: #e2ceaf;
		}
	}
	&__item {
		@extend %reset-ul-li;
		margin: 0;
	}
	&__title {
		position: relative;
		display: block;
		width: 100%;
		margin: 0;
	}
	&__name {
		position: relative;
		display: flex;
		justify-content: space-between;
		width: 100%;
		padding: 9px 45px 9px 15px;
		background-color: variables.$color-sg-black-light;
		color: #ffffff;
		font-weight: bold;
		font-size: 16px;
		text-align: left;
		cursor: pointer;

		.hoverevents &:hover {
			background-color: variables.$color-sg-black;
			color: #ffffff;
		}

		&--count {
			padding-left: 10px;
			color: variables.$color-secondary;
		}

		&::after {
			content: '';
			position: absolute;
			top: 11px;
			right: 18px;
			width: 15px;
			height: 15px;
			background: url(variables.$svg-f-plus) no-repeat;
		}

		#{$s}__group.is-open & {
			&::after {
				background-image: url(variables.$svg-f-minus);
			}
		}
	}
	&__info {
		position: absolute;
		top: variables.$spacingXs;
		right: variables.$spacingXs;
	}
	&__inner {
		padding: 0;

		& > :last-child {
			margin-bottom: 0;
		}
	}
	&__more {
		padding: 8px 12px;
		font-size: 14px;
		cursor: pointer;
	}

	&__remove {
		font-size: 14px;
	}

	&__rwd-header {
		position: relative;
		display: flow-root;
		padding: 10px 60px 10px 15px;
		border-bottom: 1px solid variables.$color-sg-gray;
		font-size: 20px;

		&::before {
			content: '';
			position: absolute;
			top: 100%;
			left: 0;
			z-index: 1;
			width: 100%;
			height: 20px;
			margin-top: 1px;
			background: linear-gradient(180deg, #ffffff 0%, rgba(#ffffff, 0) 100%);
		}
	}

	&__rwd-footer {
		position: relative;
		padding: 10px 15px;
		border-top: 1px solid variables.$color-sg-gray;

		&::after {
			content: '';
			position: absolute;
			bottom: 100%;
			left: 0;
			z-index: 1;
			width: 100%;
			height: 20px;
			margin-bottom: 1px;
			background: linear-gradient(0deg, #ffffff 0%, rgba(#ffffff, 0) 100%);
		}
	}

	.inp-item {
		&--checkbox {
			position: relative;
			display: block;
			padding: 10px 15px 10px 40px;
			color: variables.$color-sg-black-light;
			font-size: 16px;
			line-height: 20px;
			transition: background-color variables.$t;
		}

		&__count {
			color: #c6c4c1;
		}

		&__text {
			&::before,
			&::after {
				position: absolute;
				top: 11px;
				left: 11px;
			}
		}
		:disabled ~ * {
			color: #666666;
		}
	}

	&__btn {
		.icon-svg--filter {
			width: 20px;
			margin-right: 5px;
		}
	}

	// STATEs
	.js &__inner {
		display: none;
	}
	.js & .inp-item a {
		pointer-events: none;
	}
	&__group.is-open &__inner {
		display: block;
	}
	&__more.is-expanded {
		&::after {
			transform: rotate(180deg);
		}
	}

	.inp-item--checkbox.is-active,
	.hoverevents & .inp-item--checkbox:hover {
		background-color: #f3ebdf;
	}

	// MQ
	@media (config.$lg-down) {
		&__wrap {
			position: fixed;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 100;
			display: block;
			padding: variables.$modal-padding;
			border-radius: variables.$modal-radius;
			background-color: rgba(#383838, 0.8);
			overflow-x: hidden;
			overflow-y: auto;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
		}
		&__modal {
			position: relative;
			width: auto;
			height: 100%;
			max-height: 100%;
			border-radius: 2px;
			background: variables.$color-white;
			overflow: hidden;
		}
		&__content {
			position: relative;
			display: flex;
			flex-direction: column;
			width: 100%;
			height: 100%;
		}
		&__scrollable {
			z-index: 1;
			flex: 1 1 auto;
			width: 100%;
			overflow-x: hidden;
			overflow-y: auto;
			box-shadow: 0 2px 9px 0 rgba(0, 0, 0, 0.12), 0 1px 1px 0 rgba(0, 0, 0, 0.04);
		}
		&__btns {
			display: flex;
			width: 100%;
			& > * {
				flex: 1 1 50%;
				margin: 0;
			}
			.btn__text {
				padding: 11px 10px 10px;
			}
		}
		&__rwd-body {
			flex: 1 1 auto;
			padding: 20px 15px;
			overflow-x: hidden;
			overflow-y: auto;
		}
	}
}
@media (config.$lg-down) {
	.is-filter-open .f-filter__wrap {
		visibility: visible;
		opacity: 1;
	}
}
