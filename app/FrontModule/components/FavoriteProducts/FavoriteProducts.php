<?php

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace SuperKoderi\Components;

use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\Mutation;
use App\Model\Order;
use App\Model\PriceLevel;
use App\Model\State;
use App\Model\Tree;
use App\Model\User;
use App\Model\UserFavoriteProduct;
use DateTime;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use SuperKoderi\hasError500CatcherTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\LinkFactory;
use SuperKoderi\TranslatorDB;

final class FavoriteProducts extends UI\Control
{
	use hasMessageForFormComponentTrait;
	use hasOrmTrait;
	use hasError500CatcherTrait;

	private SessionSection $filterSession;

	public function __construct(
		private Tree $object,
		private User $user,
		private VisualPaginatorFactory $visualPaginatorFactory,
		private TranslatorDB $translator,
		Session $session,
		private Mutation $mutation,
		private PriceLevel $priceLevel,
		private State $state,
		private LinkFactory $linkFactory,
	)
	{
		$this->filterSession = $session->getSection('userFavoriteProductsFilter');
		if (!isset($this->filterSession->data)) {
			$this->filterSession->data = null;
		}
	}

	public function render(): void
	{
		try {
			$this->template->setTranslator($this->translator);
			$this->template->object = $this->object;
			$this->template->userEntity = $this->user;
			$this->template->mutation = $this->mutation;
			$this->template->priceLevel = $this->priceLevel;
			$this->template->state = $this->state;

			if (!$this->getComponent('pager', false)) { // ?? ajax request -> Nette\InvalidStateException Component with name 'pager' already exists.
				$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
			}

			$paginator = $this['pager']->getPaginator();

			if (!empty($_GET['favoriteProducts-pager-page'])) {
				$paginator->setPage((int)$_GET['favoriteProducts-pager-page']);
			}

			$paginator->itemsPerPage = 30;

			$favoriteProductsAll = $this->user->userFavoriteProducts->toCollection();
			$favoriteProducts = $favoriteProductsAll->limitBy($paginator->itemsPerPage, $paginator->offset);

			$paginator->itemCount = $favoriteProductsAll->countStored();

			$this->template->favoriteProducts = $favoriteProducts;
			$this->template->filterSession = $this->filterSession;

			$this->template->render(__DIR__ . '/favoriteProducts.latte');

			if ($this->presenter->isAjax()) {
				$this->redrawControl();
				$this->presenter->redrawControl();
			}

		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}

	protected function createComponentPager(): VisualPaginator
	{
		$pager = $this->visualPaginatorFactory->create();
		$pager->setTranslator($this->translator);
		return $pager;
	}


	/**
	 * @throws AbortException
	 */
	public function handleClearFilter(): void
	{
		$this->filterSession->data = null;
		$this->redirect('this');
	}


	public function handleRemoveFromFavorite(int $id): void
	{
		$userFavoriteProduct = $this->user->userFavoriteProducts->toCollection()->getById($id);
		if ($userFavoriteProduct !== null) {
			$productName = $userFavoriteProduct->product->name;
			$this->orm->removeAndFlush($userFavoriteProduct);
			$this->flashMessage("Produkt " . $productName . " byl odebraný z oblíbených.");
		}
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->redirect("this");
		}
	}

	public function getLink(UserFavoriteProduct $favoriteProduct, Mutation $mutation): string
	{
		return $this->linkFactory->linkTranslateToNette($favoriteProduct->product->getLocalization($mutation));
	}
}

interface IFavoriteProductsFactory
{
	function create(Tree $object, User $user, Mutation $mutation, PriceLevel $priceLevel, State $state): FavoriteProducts;
}
