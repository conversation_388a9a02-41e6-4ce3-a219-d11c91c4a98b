import { Controller } from '@hotwired/stimulus';
// import { useClickOutside } from 'stimulus-use';
import { disablePageScroll, enablePageScroll } from 'scroll-lock';

const body = document.querySelector('body');

export default class Menu extends Controller {
	static targets = ['menu', 'item'];

	toggle() {
		body.classList.toggle('is-menu-open');

		if (body.classList.contains('is-menu-open')) {
			disablePageScroll(body);
		} else {
			enablePageScroll(body);
			setTimeout(() => {
				this.menuTarget.classList.remove('is-submenu-open');
			}, 300);
		}
	}
	toggleSubmenu(e) {
		var target = e.currentTarget;
		target.closest('[data-menu-target="item"]').classList.toggle('is-open');
		this.menuTarget.classList.toggle('is-submenu-open');
	}
}
