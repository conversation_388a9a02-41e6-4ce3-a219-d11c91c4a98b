<?php

declare(strict_types=1);

namespace App\Model;


use App\PostType\Core\Model\LocalizationEntity;
use Nette\Utils\ArrayHash;
use SuperKoderi\hasCustomContentTrait;
use SuperKoderi\hasCustomFieldTrait;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property ArrayHash $customFieldsJson {container JsonArrayHashContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property SeoLink $seoLink {m:1 SeoLink::$localizations}
 * @property Tree|null $category {m:1 Tree, oneSided=true}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 * VIRTUAL
 * @property ArrayHash $cf {virtual}
 * @property ArrayHash $cc {virtual}
 * @property-read string $template {virtual}
 */
class SeoLinkLocalization extends RoutableEntity implements LocalizationEntity
{

	use hasCustomFieldTrait;
	use hasCustomContentTrait;

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}

	public function getParent(): SeoLink
	{
		return $this->seoLink;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	protected function getterTemplate(): string
	{
		return 'SeoLink:default';
	}

	protected function getterPath(): array
	{
		return [];
	}

}
