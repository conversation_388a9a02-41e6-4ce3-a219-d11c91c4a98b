{if $object->hasShellVariant}
	<h2>Ceny a sklad</h2>
{else}
	<h2>Varianty, ceny a sklad</h2>
{/if}

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}" n:if="$flash->type == 'error'">{$flash->message}</div>
	<br>
{/snippet}

{*{if !($variantParam1 && $variantParam2)}*}
{*Produkt bez variant*}
{*{/if}*}
<div class="crossroad-attached">
	<div class="holder">
		<div class="hd">
			{*<div class="grid-row">*}
				{*<p class="grid-1-5">*}
					{*{_ean}*}
				{*</p>*}
				{*{foreach $object->variantParameters as $parameter}*}
					{*<p class="grid-1-5">*}
						{*{$parameter->name}*}
					{*</p>*}
				{*{/foreach}*}
			{*</div>*}
		</div>
		<div class="bd">
			<ul class="sortable reset" data-copy="variants" data-pattern='{include "../form/variantRow.latte"}'>

				{foreach $object->variants as $key=>$variant}
					{include '../form/variantRow.latte', rowKey => $key, variant=>$variant}
				{/foreach}
			</ul>
		</div>
	</div>
	{if $object->variantParameters->count()}
		<div class="ft">
			<p>
				<a href="#" class="btn btn-icon-before" data-copy="variants">
					<span><span class="icon icon-plus"></span> Přidat</span>
				</a>
			</p>
		</div>
	{/if}
</div>






{*{if $object->hasVariants}*}
	{*BUTTON TO DELETE ALL VARIANTS -> create 0-0 variant*}

	{*<div class="grid-row">*}
		{*<p class="grid-1-5">*}
			{*<button name="createVariant" class="btn">*}
				{*<span>{_disable_variants}</span>*}
			{*</button>*}
		{*</p>*}

		{*ukaz selekty na vyber hodnot parametru*}
		{*{foreach $object->variantParameters as $parameter}*}
			{*<p class="grid-1-5">*}
						{*<span class="inp-fix inp-fix-select">*}
							{*<select n:name="parameter_$parameter->uid" class="inp-text">*}
							{*</select>*}
						{*</span>*}
			{*</p>*}

		{*{/foreach}*}

		{*<p class="grid-1-5">*}
			{*<button name="disableVariants" class="btn red">*}
				{*<span>{_disable_variants}</span>*}
			{*</button>*}
		{*</p>*}
	{*</div>*}

	{*ukaz radky variant*}

	{*<p>*}
	{*<span class="inp-item inp-center">*}
	{*<input type="checkbox" name="noVariants" id="noVariants" />*}
	{*<label for="noVariants">Produkt nemá varianty</label>*}
	{*</span>*}
	{*</p>*}
{*{else}*}
	{*<button name="enableVariants" class="btn"><span>{_enable_variants}</span></button>*}

	{*pokud nema zacnou variantu --> ukaz formularna vyber variantnich parametru*}

	{*ukaz radek shell variant*}


{*{/if}*}

{*<div class="grid-row">*}
	{*<div class="grid-1-2" n:if="(isset($variantParam1) && $variantParam1)  || (isset($variantParam1) && $variantParam2)">*}

		{*<p n:if="$variantParam1">*}
			{*<label for="variantParam1">Parametr 1</label>*}
			{*<span class="inp-fix inp-fix-select">*}
							{*<select name="variantParam1" class="inp-text">*}
								{*<option value="0">Bez parametru</option>*}
								{*{foreach $variantParam1 as $item}*}
									{*{php  $labelName = ""}*}
									{*{if isset($paramsParent[$item->parentId])}*}
										{*{php  $labelName = $paramsParent[$item->parentId]." > "}*}
									{*{/if}*}

									{*<optgroup label="{$labelName} {$item->name}">*}
									{*{if isset($item->values) && $item->values}*}
										{*{foreach $item->values as $value}*}
											{*<option n:if="!$value->isUsed" value="{$value->parameterId}--{$value->id}">{$value->value}</option>*}
										{*{/foreach}*}
									{*{/if}*}
									{*</optgroup>*}
								{*{/foreach}*}
							{*</select>*}
						{*</span>*}
		{*</p>*}

		{*<p n:if="$variantParam2">*}
			{*<label for="variantParam2">Parametr 2</label>*}
			{*<span class="inp-fix inp-fix-select">*}
							{*<select name="variantParam2" class="inp-text">*}
								{*<option value="0">Bez parametru</option>*}


								{*{foreach $variantParam2 as $item}*}
									{*{php  $labelName = ""}*}
									{*{if isset($paramsParent[$item->parentId])}*}
										{*{php  $labelName = $paramsParent[$item->parentId]." > "}*}
									{*{/if}*}

									{*<optgroup label="{$labelName} {$item->name}">*}
										{*{if isset($item->values) && $item->values}*}
											{*{foreach $item->values as $value}*}
												{*<option value="{$value->parameterId}--{$value->id}">{$value->value}</option>*}
											{*{/foreach}*}
										{*{/if}*}
									{*</optgroup>*}

								{*{/foreach}*}
							{*</select>*}
						{*</span>*}
		{*</p>*}
		{*<p>*}
			{*<button name="insertVariant" class="btn"><span>Přidat</span></button>*}
		{*</p>*}
	{*</div>*}

	{*<div class="grid-1-2">*}

	{*</div>*}
{*</div>*}

{*<br>*}
{*<table n:if="$object->variants->count() > 0">*}
	{*<caption>{if $object->variants->count()>1}Varianty{else}Varianta{/if}</caption>*}

	{*{php  $isVariants = TRUE}*}

	{*{if $object->firstVariant && !$object->firstVariant->param1ValueId && !$object->firstVariant->param2ValueId}*}
		{*{php  $isVariants = FALSE}*}
	{*{/if}*}

	{*<tr>*}
		{*<th>ID</th>*}
		{*<th n:if="$isVariants && isset($variantParam1[$object->firstVariant->param1Id])">Parametr 1 {if $object->firstVariant->param1Id} ({$variantParam1[$object->firstVariant->param1Id]->name}) {/if}</th>*}
		{*<th n:if="$isVariants && isset($variantParam2[ $object->firstVariant->param2Id])">Parametr 2 {if $object->firstVariant->param2Id} ({$variantParam2[$object->firstVariant->param2Id]->name}) {/if}</th>*}
		{*<th n:if="$config['shop']['enableVariantPrice']">Cena s DPH</th>*}
		{*<th>Kusů skladem</th>*}
		{*<th>Na dotaz</th>*}
		{*<th>EAN</th>*}
		{*<th>Smazat</th>*}
	{*</tr>*}
	{*{foreach $variants as $v}*}
		{*<tr>*}
			{*<td>{$v->id}</td>*}
			{*<td n:if="$isVariants && isset($variantParam1[$v->param1Id]->values[$v->param1ValueId])">*}
				{*{if $v->param1Id}{$variantParam1[$v->param1Id]->name}: {/if}*}
				{*<strong>*}
					{*{if $v->param1ValueId}{$variantParam1[$v->param1Id]->values[$v->param1ValueId]->value}{/if}*}
				{*</strong>*}
			{*</td>*}

			{*<td n:if="$isVariants && isset($variantParam2[$v->param2Id]->values[$v->param2ValueId])">*}
				{*{if $v->param2Id}{$variantParam2[$v->param2Id]->name}: {/if}*}
				{*<strong>*}
					{*{if $v->param2ValueId}{$variantParam2[$v->param2Id]->values[$v->param2ValueId]->value}{/if}*}
				{*</strong>*}
			{*</td>*}

			{*<td n:if="$config['shop']['enableVariantPrice']">*}
				{*<input type="text" name="variant[{$v->id}][priceDPH]" value="{$v->priceDPH}" size="4"*}
				       {*id="variant[{$v->id}][priceDPH]" class="inp-text" />*}
			{*</td>*}

			{*<td>*}
				{*<input type="text" name="variant[{$v->id}][stockCount]" value="{$v->stockCount}" size="4"*}
				       {*id="variant[{$v->id}][stockCount]" class="inp-text" />*}
			{*</td>*}

			{*<td>*}
				{*<input type="checkbox" name="variant[{$v->id}][onAsk]"*}
				       {*id="variant[onAsk][{$v->id}]" class="inp-text"{if$v->onAsk} checked="checked"{/if} />*}
			{*</td>*}

			{*<td>*}
				{*<input type="text" name="variant[{$v->id}][ean]" value="{$v->ean}"*}
				       {*id="variant[{$v->id}][ean]" class="inp-text"  />*}
			{*</td>*}

			{*<td>*}
				{*<input type="checkbox" name="deleteVariant[{$v->id}]"*}
				       {*id="deleteVariant[{$v->id}]" class="inp-text" />*}
			{*</td>*}
		{*</tr>*}
	{*{/foreach}*}
{*</table>*}

{*<button n:if="isset($variants) && $variants" name="updateVariant" class="btn"><span>Hotovo</span></button>*}