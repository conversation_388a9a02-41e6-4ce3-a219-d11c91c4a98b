<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $amount {default 0}
 * @property DateTimeImmutable|null $lastImport
 *
 *
 * RELATIONS
 * @property Stock $stock {m:1 Stock::$supplies}
 * @property ProductVariant $variant {m:1 ProductVariant::$supplies}
 *
 *
 * VIRTUALS
 */
class Supply extends Entity
{

}
