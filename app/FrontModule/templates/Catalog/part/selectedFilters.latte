{default $class = 'u-mb-sm'}

{if isset($filter->nonRoot) && $filter->nonRoot}
	<div class="b-filters {$class}">
		<div class="b-filters__wrap">
			<div class="b-filters__filters">
				{if isset($filter->items)}
					{foreach $filter->items as $filterItem}
						{switch $filterItem->type}
							{case 'dial'}
								{if $filterItem->activeValues}
									{include './selectedPart/dial.latte', filterItem=>$filterItem, cleanFilterParam=>$cleanFilterParam}
								{/if}
							{case 'flag'}
								{if $filterItem->isActive}
									{include './selectedPart/flag.latte', filterItem=>$filterItem, cleanFilterParam=>$cleanFilterParam}
								{/if}
							{case 'flagValue'}
								{if $filterItem->activeValues}
									{include './selectedPart/flagWithValues.latte', filterItem=>$filterItem, cleanFilterParam=>$cleanFilterParam}
								{/if}
							{case 'range'}
								{if $filterItem->inputValueMin != $filterItem->selectedMin || $filterItem->inputValueMax != $filterItem->selectedMax}
									{include './selectedPart/range.latte', filterItem=>$filterItem, cleanFilterParam=>$cleanFilterParam}
								{/if}
						{/switch}
					{/foreach}
				{/if}
			</div>

			<p>

				{capture $link}{link 'this', 'filter' => $filter->followingCleanFilterParameters, 'pager-page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}

				<a href="{$link}">
					{_btn_filter_remove}
				</a>
			</p>
		</div>
	</div>
{/if}
