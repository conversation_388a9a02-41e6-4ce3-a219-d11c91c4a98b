{default $class = 'u-mb-lg'}
{default $pager = true}
{default $title = 'h2'}
{default $customTitle = false}
{default $products = false}
{default $ajaxPage = false}
{default $cleanFilterParam = []}

{if $products}
	<section class="c-products {$class}">
		{if $customTitle}
			<{$title}>
				{translate}{$customTitle}{/translate}
			</{$title}>
		{/if}

		{php $cleanFilterParamCopy = $cleanFilterParam}

		{* {if $pager}
			{snippet productsPagerTop}
				{default $cleanFilterParam = []}
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{control pager, [filter => $cleanFilterParamCopy, showPages=>true]}
			{/snippet}
		{/if} *}

		<div class="c-products__list grid" n:snippet="productList" data-ajax-append>
			{foreach $products as $product}
				<div class="c-products__item grid__cell size--6-12@md size--4-12@xl">
					{include '../box/product.latte', product => $product, loading => $iterator->counter > 3 ? 'lazy' : 'eager'}
				</div>
			{/foreach}
			{if !(count($products))}
				<div class="grid__cell">
					<div class="message message--error">
						{_'msg_no_products'}
					</div>
				</div>
			{/if}
		</div>

		{if $pager}
			{snippet productsPagerBottom}
				{default $cleanFilterParam = []}
				{php $cleanFilterParamCopy = $cleanFilterParam}
				{default $ajaxPage = false}
				{control pager, [filter => $cleanFilterParamCopy, showMoreBtn=>true, ajaxPage=>$ajaxPage]}
			{/snippet}
		{/if}
	</section>
{else}
	<div class="message {$class}">
		{_message_empty_filter}
	</div>
{/if}

