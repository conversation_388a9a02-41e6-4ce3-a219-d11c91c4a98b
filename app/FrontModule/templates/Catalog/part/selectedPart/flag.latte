
<div class="b-filters__group">
	<p class="b-filters__title">
		{$filterItem->title}:
	</p>

	{capture $link}{link 'this', 'filter' => $filterItem->filterToDeSelect, 'pager-page' => null}{/capture}
	{php $link = urldecode(htmlspecialchars_decode($link))}
	<p>
		<a href="{$link}" data-naja data-naja-loader="body">
			{_btn_filter_cancel}
		</a>
	</p>

	<ul class="b-filters__list">
		{capture $link}{link 'this', filter => $filterItem->filterToDeSelect}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}

		<li class="b-filters__item">
			<a href="{$link}" class="b-filters__remove" data-naja data-naja-loader="body"{if $linkSeo->hasNofollow($object, ['filter' => $filterItem->filterToDeSelect])} rel="nofollow"{/if}>
				{_'yes'}
			</a>
		</li>
	</ul>
</div>
