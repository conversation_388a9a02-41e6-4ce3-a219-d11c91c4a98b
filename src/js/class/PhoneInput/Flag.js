export class Flag {
	constructor(options, countryCode) {
		this.options = options;
		this.countryCode = countryCode;
	}

	get() {
		if (this.element) return this.element;
		return this.create();
	}

	create() {
		const { className } = this.options;
		this.element = document.createElement('span');
		this.element.classList.add(`${className}__flag`);
		this.image = document.createElement('img');
		this.image.setAttribute('src', this.getFlagSrc());
		this.element.appendChild(this.image);

		return this.element;
	}

	setCountry(countryCode) {
		this.countryCode = countryCode;
		this.image.setAttribute('src', this.getFlagSrc());
	}

	getFlagSrc() {
		const { flagPath, flagExt } = this.options;
		return `${flagPath}${this.countryCode.toUpperCase()}${flagExt}`;
	}
}
