<?php declare(strict_types = 1);

namespace App\Model;

use App\PostType\Core\Model\ParentEntity;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\BucketFilter\CatalogParameter;
use SuperKoderi\hasTranslatorTrait;

/**
 * @property int|null $hasLinkedCategories {default 0}
 *
 *
 * RELATIONS
 *
 *
 * VIRTUALS
 * @property-read Parameter[]|array $importantParameters {virtual}
 *
 * @property-read Tree[]|ICollection $linkedCategories {virtual}
 * @property-read Tree[]|ICollection $linkedCategoriesAll {virtual}
 * @property Tree[]|ICollection $parentLinkedCategories {virtual}
 * @property Tree[]|ICollection $parentLinkedCategoriesAll {virtual}
 */
final class CatalogTree extends Tree
{

	use hasTranslatorTrait;

	private CatalogParameter $catalogParameter;

	public function injectCatalogParameter(CatalogParameter $catalogParameter): void
	{
		$this->catalogParameter = $catalogParameter;
	}


	protected function getterLinkedCategories(): ICollection
	{
		return $this->linkedCategoriesAll->findBy($this->treeRepository->getPublicOnlyWhere());
	}


	public function getterLinkedCategoriesAll(): ICollection
	{
		return $this->treeRepository->findAttachedTreesInRelations($this, TreeTree::TYPE_LINKED_CATEGORY);
	}


	public function getterParentLinkedCategories(): ICollection
	{
		return $this->parentLinkedCategoriesAll->findBy($this->treeRepository->getPublicOnlyWhere());
	}


	public function getterParentLinkedCategoriesAll(): ICollection
	{
		return $this->treeRepository->findMainTreesInRelations($this, TreeTree::TYPE_LINKED_CATEGORY);
	}




	protected function getterImportantParameters(): array
	{
		if (!isset($this->cache['importantParameters'])) {
			$this->cache['importantParameters'] = $this->catalogParameter->getPossibleIndexableParametersForCatalog($this)->fetchPairs('uid', null);
		}

		return $this->cache['importantParameters'];
	}


	public function isImportantParameter(string $name): bool
	{
		return isset($this->importantParameters[$name]);
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getInternalName(): string
	{
		return $this->name;
	}

	public function setInternalName(string $internalName): void
	{
		$this->name = $internalName;
	}


}
