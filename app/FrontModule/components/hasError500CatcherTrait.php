<?php

namespace SuperKoderi;

use Nette\Application\Application;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Throwable;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

/**
 * @property-read DefaultTemplate $template
 */
trait hasError500CatcherTrait
{

	use hasConfigServiceTrait;

	protected function handleRenderError500(Throwable $e): void
	{
		Debugger::log($e, ILogger::ERROR);

		// Debug mode + DEV: neon - catchExceptions: true = zobrazi se 500 sablona; false = zobrazi se Tracy
		if ($this->configService->isDev()) {
			$application = $this->presenter->context->getByType(Application::class, false);

			if (!$application || !$application->catchExceptions) {
				throw $e;
			}
		}

		$this->template->render(FE_TEMPLATE_DIR . '/Error/500inner.latte');
	}

}
