<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method EsIndex getById($id)
 */
final class EsIndexRepository extends Repository
{

	static function getEntityClassNames(): array
	{
		return [EsIndex::class];
	}

	public function getLastActive(string $type, ?Mutation $mutation = null): ?EsIndex
	{
		return $this->findBy([
			'mutation' => $mutation,
			'type' => $type,
			'active' => 1,
		])->orderBy('id', ICollection::DESC)->fetch();
	}


	public function getAllLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_ALL, $mutation);
	}


	public function getCommonLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_COMMON, $mutation);
	}

	public function getProductLastActive(Mutation $mutation): ?EsIndex
	{
		return $this->getLastActive(EsIndex::TYPE_PRODUCT, $mutation);
	}


	public function findActiveByType(string $type): ICollection
	{
		return $this->findBy([
			'active' => 1,
			'type' => $type,
		]);
	}

}
