{"name": "start_project", "version": "4.6.0", "sideEffects": false, "browserslist": ["since 2020", "not < 0.01%", "not QQAndroid > 1"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@babel/core": "7.17.8", "@babel/plugin-transform-runtime": "7.17.0", "@babel/preset-env": "7.16.11", "@superkoders/eslint-config": "2.1.1", "@superkoders/prettier-config": "0.2.6", "@superkoders/stylelint-config": "2.1.0", "ansi-colors": "4.1.1", "autoprefixer": "10.4.4", "babel-loader": "8.2.4", "babel-loader-exclude-node-modules-except": "1.2.1", "browser-sync": "2.27.9", "cheerio": "1.0.0-rc.10", "deepmerge": "4.2.2", "del": "6.0.0", "eslint": "8.12.0", "fancy-log": "2.0.0", "gulp": "4.0.2", "gulp-cheerio": "1.0.0", "gulp-consolidate": "^0.2.0", "gulp-format-html": "1.2.5", "gulp-imagemin": "8.0.0", "gulp-plumber": "^1.2.1", "gulp-postcss": "9.0.1", "gulp-rename": "2.0.0", "gulp-sass": "^5.1.0", "gulp-svgmin": "4.1.0", "gulp-svgstore": "9.0.0", "gulp-twing": "4.0.0", "gulp-w3c-html-validator": "5.1.2", "gulp-zip": "5.1.0", "husky": "7.0.4", "import-fresh": "3.3.0", "lint-staged": "12.3.7", "lodash": "4.17.21", "node-notifier": "10.0.1", "parse-sass-value": "2.3.0", "postcss": "8.4.12", "sass": "1.49.10", "through2": "4.0.2", "twing": "5.1.1", "vinyl": "2.2.1", "webpack": "5.71.0", "webpack-cli": "6.0.1"}, "dependencies": {"@hotwired/stimulus": "3.0.1", "@superkoders/modal": "1.7.0", "@superkoders/sk-tools": "1.7.1", "embla-carousel": "7.0.3", "embla-carousel-autoplay": "7.0.3", "gulp-real-favicon": "0.3.2", "libphonenumber-js": "1.10.13", "lite-youtube-embed": "0.2.0", "naja": "2.1.5", "nouislider": "15.6.0", "scroll-lock": "2.1.5", "stimulus-use": "0.50.0", "uuid": "9.0.0", "wnumb": "1.2.0"}, "scripts": {"preversion": "npm run build", "version": "git add -A", "postversion": "git push --follow-tags", "export": "npx gulp export", "build": "npx gulp min", "dev": "npx gulp", "prestart": "npm install", "preinstall": "npx check-engine", "start": "npm run dev", "lint:css": "stylelint \"src/**/*.{css,scss}\" || exit 0", "lint:css:fix": "prettier --write \"src/**/*.{css,scss}\" && stylelint --fix \"src/**/*.{css,scss}\"", "lint:js": "eslint . || exit 0", "lint:js:fix": "eslint . --fix", "print-version": "echo $npm_package_version", "prepare": "husky install"}, "lint-staged": {"*.scss": ["stylelint \"src/**/*.{css,scss}\""], "*.js": ["eslint"]}, "volta": {"node": "16.14.0", "npm": "8.6.0"}}