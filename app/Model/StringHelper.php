<?php declare(strict_types = 1);

namespace SuperKoderi;

use function preg_replace;

class StringHelper
{

	/**
	 * Remove empty p tag at the end of content (content is generated in tinyMce)
	 */
	public static function removeTinyMceEmptyP(string $s): string
	{
		return preg_replace('/(<p>\xC2\xA0<\/p>(\r?\n))*<p>\xC2\xA0<\/p>$/', '', $s);
	}

	public static function intTonull(int $value): ?int
	{
		return $value === 0 ? null : $value;
	}

}
