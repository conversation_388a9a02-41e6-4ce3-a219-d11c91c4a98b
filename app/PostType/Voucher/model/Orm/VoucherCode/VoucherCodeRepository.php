<?php


namespace App\Model;

/**
 * @method VoucherCode getById($id)
 */
final class VoucherCodeRepository extends \Nextras\Orm\Repository\Repository
{
	static function getEntityClassNames(): array
	{
		return [VoucherCode::class];
	}

	public function getByCode(string $code, Mutation $mutation): ?VoucherCode
	{
		return $this->getBy([
			'code' => strtoupper(trim($code)),
			'voucher->mutation->id' => $mutation->id
		]);
	}

}
