<?php declare(strict_types = 1);

namespace App\Model\Erp;

use Curl\Curl;
use Nette\Utils\Json;


class MssqlCaller
{

	public function __construct(
		private readonly string $env
	)
	{
	}

	public function callRemoteQuery(string $sql): array|\stdClass
	{
		$hash = 'WsgQHNsZXZ5ID0gJzEwOzExOzE';
		$url = 'https://www.zameckevinarstvi.cz/api/ms';

		$curl = $this->getCurl();

		$curl->post($url, [
			'query' => base64_encode($sql),
			'hash' => $hash,
		]);

		if ($curl->error) {
			throw new \Exception($curl->errorMessage);
		} else {
			if ($curl->response) {
				return $curl->response->rows ?? $curl->response;
			} else {
				return [];
			}
		}
	}

	public function callRemoteQueryFromBuilder(array $infosOrderRequestArray): array|\stdClass
	{
		$hash = 'WsgQHNsZXZ5ID0gJzEwOzExOzE';
		$url = 'https://www.zameckevinarstvi.cz/api/ms';

		$curl = $this->getCurl();

		$curl->post($url, [
			'queryFromBuilder' => base64_encode(Json::encode($infosOrderRequestArray)),
			'hash' => $hash,
		]);

		if ($curl->error) {
			throw new \Exception($curl->errorMessage);
		} else {
			if ($curl->response) {
				return $curl->response->rows ?? $curl->response;
			} else {
				return [];
			}
		}
	}

	/**
	 * @return Curl
	 */
	private function getCurl(): Curl
	{
		$curl = new Curl();
		if (!in_array($this->env, ['stage', 'production'])) {
			curl_setopt($curl->curl, CURLOPT_SSL_VERIFYHOST, false); // DANGER!
			curl_setopt($curl->curl, CURLOPT_SSL_VERIFYPEER, false); // DANGER!
		}
		return $curl;
	}
}
