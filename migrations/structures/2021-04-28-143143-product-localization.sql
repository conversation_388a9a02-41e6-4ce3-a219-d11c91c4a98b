ALTER TABLE `product`
DROP `descriptionEn`,
DROP `descriptionSk`,
DROP `keywordsEn`,
DROP `keywordsSk`,
DROP `nameEn`,
DROP `nameTitleEn`,
DROP `nameAnchorEn`,
DROP `annotationEn`,
DROP `contentEn`,
DROP `nameSk`,
DROP `nameTitleSk`,
DROP `nameAnchorSk`,
DROP `annotationSk`,
DROP `contentSk`;


CREATE TABLE `product_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`productId` INT(11) NOT NULL,
	`mutationId` INT(11) NOT NULL,
	`public` INT(11) NULL DEFAULT '0',
	`name` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_bin',
	`nameTitle` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_bin',
	`nameAnchor` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_bin',
	`description` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`keywords` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`annotation` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`content` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`videos` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`links` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`setup` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `mutationId_productId` (`mutationId`, `productId`) USING BTREE,
	INDEX `productId` (`productId`) USING BTREE,
	CONSTRAINT `product_localization_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `superadmin2`.`product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `product_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `superadmin2`.`mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
COLLATE='utf8_bin'
ENGINE=InnoDB
AUTO_INCREMENT=1
;


ALTER TABLE `product`
DROP `nameTitle`,
DROP `nameAnchor`,
DROP `description`,
DROP `keywords`,
DROP `annotation`,
DROP `content`;


ALTER TABLE `product`
CHANGE `name` `internalName` varchar(255) COLLATE 'utf8_general_ci' NULL AFTER `ean`;





ALTER TABLE `product`
DROP `alexTipText`;

ALTER TABLE `product`
DROP `rentPrice`,
DROP `rentPriceDPH`,
DROP `rentCount`;


ALTER TABLE `product`
DROP `isInsurance`,
DROP `isRent`;


ALTER TABLE `product`
DROP `code`,
DROP `ean`;


ALTER TABLE `product_variant`
DROP `imageResolution`,
DROP `lensLuminance`,
DROP `remoteRange`,
DROP `innerStorage`,
DROP `batteryTime`,
DROP `angle`,
DROP `speed`;
