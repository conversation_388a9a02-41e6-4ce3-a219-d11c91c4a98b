{% set props = {
	texts: props.texts | default([]),
	inps: props.inps | default([]),
	img: props.img | default,
	checkboxes: props.checkboxes | default([]),
	btnsBefore: props.btnsBefore | default([]),
	btnsAfter: props.btnsAfter | default([]),
	tags: props.tags | default([]),
	langs: props.langs | default([]),
	data: props.data | default(null),
	dragdrop: props.dragdrop | default(false),
	progress: props.progress | default(false),
} %}

<div
	class="b-list__item"
	{% if props.data is not null %}
		{% for key, value in props.data %}
			data-{{ key }}="{{ value }}"
		{% endfor %}
	{% endif %}
>
	{% if props.progress %}
		<div class="b-list__progress" data-file-target="progress"></div>
	{% endif %}
	{% if props.dragdrop %}
		<button class="b-list__dragdrop btn-icon btn-icon--grab" data-drag-handle type="button">
			{% include '@icons/grip-vertical.svg' %}
		</button>
	{% endif %}
	{% for btn in props.btnsBefore %}
		{% set btn = {
			icon: btn.icon | default('@icons/user.svg'),
			data: btn.data | default(null),
			tooltip: btn.tooltip | default,
			type: btn.type | default in ['checkbox'] ? btn.type,
			variant: btn.variant | default in ['remove'] ? btn.variant,
			classes: btn.classes | default(['']),
		} %}

		{% set classesBtn = [
			'b-list__btn',
			'btn-icon',
			btn.type ? 'btn-icon--' ~ btn.type,
			btn.variant ? 'btn-icon--' ~ btn.variant,
			btn.tooltip ? 'tooltip',
		] | merge(btn.classes) | filter(i => i) | join(' ') %}

		<button
			type="button"
			class="{{ classesBtn }}"
			{% if btn.data is not null %}
				{% for key, value in btn.data %}
					data-{{ key }}="{{ value }}"
				{% endfor %}
			{% endif %}
		>
			{% include btn.icon %}
			{% if btn.tooltip %}
				<span class="tooltip__content">
					{{ btn.tooltip }}
				</span>
			{% endif %}
		</button>
	{% endfor %}
	{% if props.img %}
		<span class="b-list__img">
			<img src="{{ props.img }}" alt="">
		</span>
	{% endif %}
	{% for text in props.texts %}
		{% set text = {
			text: text.text | default,
			classes: inp.classes | default,
		} %}
		<span class="b-list__text {{ text.classes }}">
			{{ text.text|raw }}
		</span>
	{% endfor %}
	{% for inp in props.inps %}
		{% set inp = {
			id: inp.id | default,
			placeholder: inp.placeholder | default('@icons/user.svg'),
			value: inp.value | default,
			classes: inp.classes | default,
			disabled: inp.disabled | default(false),
			data: inp.data | default(null),
		} %}
		<span class="b-list__inp {{ inp.classes }}">
			<input type="text" class="inp-text" name="{{ inp.id }}" value="{{ inp.value }}" placeholder="{{ inp.placeholder }}"
				{% if inp.disabled %}
					disabled
				{% endif %}
				{% if inp.data is not null %}
					{% for key, value in inp.data %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}
			>
		</span>
	{% endfor %}
	{% if props.langs is not empty %}
		<span class="b-list__langs" data-ProductVariant-target="langs">
			{% for lang in props.langs %}
				<span class="tag js-lang js-lang--{{ lang | lower }}">
					{{ lang }}
				</span>
			{% endfor %}
		</span>
	{% endif %}
	<span class="b-list__tags" data-File-target="tags">
		{% for tag in props.tags %}
			<span class="tag">
				{{ tag.text }}
			</span>
		{% endfor %}
	</span>
	{% for checkbox in props.checkboxes %}
		{% set checkbox = {
			icon: checkbox.icon | default('@icons/user.svg'),
			tooltip: checkbox.tooltip | default
		} %}

		{% include '@components/core/checkbox.twig' with {
			props: {
				icon: checkbox.icon,
				tooltip: checkbox.tooltip,
				variant: 'icon',
				classes: [''],
			}
		} %}
	{% endfor %}
	{% for btn in props.btnsAfter %}
		{% set btn = {
			icon: btn.icon | default('@icons/user.svg'),
			data: btn.data | default(null),
			tooltip: btn.tooltip | default,
			type: btn.type | default in ['checkbox'] ? btn.type,
			variant: btn.variant | default in ['remove'] ? btn.variant,
			classes: btn.classes | default(['']),
		} %}

		{% set classesBtn = [
			'b-list__btn',
			'btn-icon',
			btn.type ? 'btn-icon--' ~ btn.type,
			btn.variant ? 'btn-icon--' ~ btn.variant,
			btn.tooltip ? 'tooltip',
		] | merge(btn.classes) | filter(i => i) | join(' ') %}

		<button
			type="button"
			class="{{ classesBtn }}"
			{% if btn.data is not null %}
				{% for key, value in btn.data %}
					data-{{ key }}="{{ value }}"
				{% endfor %}
			{% endif %}
		>
			{% include btn.icon %}
			{% if btn.tooltip %}
				<span class="tooltip__content">
					{{ btn.tooltip }}
				</span>
			{% endif %}
		</button>
	{% endfor %}
</div>
